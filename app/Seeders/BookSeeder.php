<?php

declare(strict_types=1);

namespace App\Seeders;

use App\Models\Author;
use App\Models\AuthorType;
use App\Models\Book;
use App\Models\Chapter;
use App\Models\Isbn;
use App\Models\Publisher;
use Tempest\Database\DatabaseSeeder;
use UnitEnum;

use function Tempest\Database\query;

class BookSeeder implements DatabaseSeeder
{
    public function run(UnitEnum|string|null $database): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Greece Publishers', description: 'The Greece Publishers Guild')
            ->onDatabase($database)
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Homer', type: AuthorType::A, publisher_id: $publisher_id)
            ->onDatabase($database)
            ->execute();

        $book_id = query(Book::class)
            ->insert(title: 'Homer', author_id: $author_id)
            ->onDatabase($database)
            ->execute();

        query(Isbn::class)
            ->insert(value: '1943484343489283', book_id: $book_id)
            ->onDatabase($database)
            ->execute();

        query(Chapter::class)
            ->insert(title: 'Chapter 1', contents: 'Fooba baa foo baaaah', book_id: $book_id)
            ->onDatabase($database)
            ->execute();
    }
}

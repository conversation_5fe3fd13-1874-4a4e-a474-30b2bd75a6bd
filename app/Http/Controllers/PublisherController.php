<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\CreatePublisherRequest;
use App\Http\Requests\UpdatePublisherRequest;
use App\Models\Publisher;
use Tempest\Http\Responses\Redirect;
use Tempest\Http\Session\Session;
use Tempest\Router\Delete;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\Router\Put;
use Tempest\View\View;

use function Tempest\view;

final readonly class PublisherController
{
    public function __construct(
        private Session $session,
    ) {}
    #[Get('/publishers')]
    public function index(): View
    {
        $publishers = Publisher::select()->all();

        // Ensure publishers is never null
        $publishers = $publishers ?? [];

        return view('Views/Publishers/index.view.php', title: 'Publisher Listing', publishers: $publishers);
    }

    #[Get('/publishers/{publisherId}')]
    public function show(int $publisherId): View|Redirect
    {
        $publisher = Publisher::find(id: $publisherId)->first();

        // Handle case where publisher is not found
        if ($publisher === null) {
            // Publisher not found - redirect to publishers list instead of showing broken page
            return new Redirect('/publishers');
        }

        return view('Views/Publishers/show.view.php', title: 'Publisher Details', publisher: $publisher);
    }

    #[Get('/publishers/create')]
    public function create(): View
    {
        // Get validation errors and original values from session with null safety
        $sessionErrors = $this->session->get(Session::VALIDATION_ERRORS, []);
        $sessionOriginalValues = $this->session->get(Session::ORIGINAL_VALUES, []);

        // Ensure session data is always an array
        $errors = is_array($sessionErrors) ? $sessionErrors : [];
        $originalValues = is_array($sessionOriginalValues) ? $sessionOriginalValues : [];

        return view('Views/Publishers/create.view.php',
            title: 'Create Publisher',
            errors: $errors,
            originalValues: $originalValues
        );
    }

    #[Post('/publishers')]
    public function store(CreatePublisherRequest $request): Redirect
    {
        // Validate required request parameters are not null
        if ($request->name === null || $request->description === null) {
            // This should be handled by validation, but adding defensive check
            return new Redirect('/publishers/create');
        }

        Publisher::create(name: $request->name, description: $request->description);

        return new Redirect('/publishers');
    }

    #[Get('/publishers/{publisherId}/edit')]
    public function edit(int $publisherId): View|Redirect
    {
        $publisher = Publisher::find(id: $publisherId)->first();

        // Handle case where publisher is not found
        if ($publisher === null) {
            // Publisher not found - redirect to publishers list instead of showing broken page
            return new Redirect('/publishers');
        }

        // Get validation errors and original values from session with null safety
        $sessionErrors = $this->session->get(Session::VALIDATION_ERRORS, []);
        $sessionOriginalValues = $this->session->get(Session::ORIGINAL_VALUES, []);

        // Ensure session data is always an array
        $errors = is_array($sessionErrors) ? $sessionErrors : [];
        $originalValues = is_array($sessionOriginalValues) ? $sessionOriginalValues : [];

        return view('Views/Publishers/edit.view.php',
            title: 'Edit Publisher',
            publisher: $publisher,
            errors: $errors,
            originalValues: $originalValues
        );
    }

    #[Put('/publishers/{publisherId}')]
    public function update(int $publisherId, UpdatePublisherRequest $request): Redirect
    {
        // Validate required request parameters are not null
        if ($request->name === null || $request->description === null) {
            // This should be handled by validation, but adding defensive check
            return new Redirect('/publishers/' . $publisherId . '/edit');
        }

        $publisher = Publisher::find(id: $publisherId)->first();

        // Ensure publisher exists
        if ($publisher === null) {
            // Publisher not found - redirect to publishers list
            return new Redirect('/publishers');
        }

        $publisher->name = $request->name;
        $publisher->description = $request->description;
        $publisher->save();

        return new Redirect('/publishers/' . $publisherId);
    }

    #[Delete('/publishers/{publisherId}')]
    public function delete(int $publisherId): Redirect
    {
        $publisher = Publisher::find(id: $publisherId)->first();

        // Explicit null check with better defensive programming
        if ($publisher !== null) {
            $publisher->delete();
        }
        // If publisher is null, we silently continue - publisher might already be deleted
        // or never existed, which is fine for a delete operation

        return new Redirect('/publishers');
    }
}

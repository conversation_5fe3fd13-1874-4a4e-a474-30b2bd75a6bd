<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\CreateBookRequest;
use App\Http\Requests\UpdateBookRequest;
use App\Models\Author;
use App\Models\Book;
use App\Models\Publisher;
use Tempest\Http\Responses\Redirect;
use Tempest\Http\Session\Session;
use Tempest\Router\Delete;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\Router\Put;
use Tempest\View\View;

use function Tempest\view;

final readonly class HomeController
{
    public function __construct(
        private Session $session,
    ) {}
    #[Get('/')]
    #[Get('/books')]
    public function __invoke(): View
    {
        $books = Book::select()
            ->with('author.publisher')
            ->all();

        // Ensure books is never null
        $books = $books ?? [];

        return view('Views/Books/index.view.php', title: 'Book Listing', books: $books);
    }

    #[Get('/books/{bookId}')]
    public function show(int $bookId): View|Redirect
    {
        $book = Book::find(id: $bookId)
            ->with('author.publisher')
            ->first();

        // Handle case where book is not found
        if ($book === null) {
            // Book not found - redirect to home page instead of showing broken page
            return new Redirect('/');
        }

        return view('Views/Books/show.view.php', title: 'Details', book: $book);
    }

    #[Get('/books/create')]
    public function create(): View
    {
        $publishers = Publisher::select()->all();
        $authors = Author::select()->all();

        // Ensure database results are never null
        $publishers = $publishers ?? [];
        $authors = $authors ?? [];

        // Get validation errors and original values from session with null safety
        $sessionErrors = $this->session->get(Session::VALIDATION_ERRORS, []);
        $sessionOriginalValues = $this->session->get(Session::ORIGINAL_VALUES, []);

        // Ensure session data is always an array
        $errors = is_array($sessionErrors) ? $sessionErrors : [];
        $originalValues = is_array($sessionOriginalValues) ? $sessionOriginalValues : [];

        return view('Views/Books/create.view.php',
            title: 'Create Book',
            authors: $authors,
            publishers: $publishers,
            errors: $errors,
            originalValues: $originalValues
        );
    }

    #[Post('/books')]
    public function store(CreateBookRequest $request): Redirect
    {
        // Validate request parameters are not null
        if ($request->authorId === null || $request->title === null) {
            // This should be handled by validation, but adding defensive check
            return new Redirect('/books/create');
        }

        $author = Author::find(id: $request->authorId)->first();

        // Ensure author exists before creating book
        if ($author === null) {
            // Author not found - this should be handled by validation
            // but adding defensive programming
            return new Redirect('/books/create');
        }

        Book::create(title: $request->title, author: $author);

        return new Redirect('/');
    }

    #[Get('/books/{bookId}/edit')]
    public function edit(int $bookId): View|Redirect
    {
        $book = Book::find(id: $bookId)
            ->with('author.publisher')
            ->first();

        if ($book === null) {
            return new Redirect('/');
        }

        // Get validation errors and original values from session with null safety
        $sessionErrors = $this->session->get(Session::VALIDATION_ERRORS, []);
        $sessionOriginalValues = $this->session->get(Session::ORIGINAL_VALUES, []);

        // Ensure session data is always an array
        $errors = is_array($sessionErrors) ? $sessionErrors : [];
        $originalValues = is_array($sessionOriginalValues) ? $sessionOriginalValues : [];

        return view('Views/Books/edit.view.php',
            title: 'Edit Book',
            book: $book,
            authors: Author::select()->all() ?? [],
            publishers: Publisher::select()->all() ?? [],
            errors: $errors,
            originalValues: $originalValues
        );
    }

    #[Put('/books/{bookId}')]
    public function update(int $bookId, UpdateBookRequest $request): Redirect
    {
        $book = Book::find(id: $bookId)->first();

        // Ensure book exists
        if ($book === null) {
            return new Redirect('/');
        }

        $author = Author::find(id: $request->authorId)->first();

        // Ensure author exists
        if ($author === null) {
            // Author not found - redirect back to edit form
            return new Redirect('/books/' . $bookId . '/edit');
        }

        $book->title = $request->title;
        $book->author = $author;
        $book->save();

        return new Redirect('/books/' . $bookId);
    }

    #[Delete('/books/{bookId}')]
    public function delete(int $bookId): Redirect
    {
        $book = Book::find(id: $bookId)->first();

        // Explicit null check with better defensive programming
        if ($book !== null) {
            $book->delete();
        }
        // If book is null, we silently continue - book might already be deleted
        // or never existed, which is fine for a delete operation

        return new Redirect('/');
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\CreateAuthorRequest;
use App\Http\Requests\UpdateAuthorRequest;
use App\Models\Author;
use App\Models\Publisher;
use Tempest\Http\Responses\Redirect;
use Tempest\Http\Session\Session;
use Tempest\Router\Delete;
use Tempest\Router\Get;
use Tempest\Router\Post;
use Tempest\Router\Put;
use Tempest\View\View;

use function Tempest\view;

final readonly class AuthorController
{
    public function __construct(
        private Session $session,
    ) {}
    #[Get('/authors')]
    public function index(): View
    {
        $authors = Author::select()
            ->with('publisher')
            ->all();

        // Ensure authors is never null
        $authors = $authors ?? [];

        return view('Views/Authors/index.view.php', title: 'Author Listing', authors: $authors);
    }

    #[Get('/authors/{authorId}')]
    public function show(int $authorId): View|Redirect
    {
        $author = Author::find(id: $authorId)
            ->with('publisher', 'books')
            ->first();

        // Handle case where author is not found
        if ($author === null) {
            // Author not found - redirect to authors list instead of showing broken page
            return new Redirect('/authors');
        }

        return view('Views/Authors/show.view.php', title: 'Author Details', author: $author);
    }

    #[Get('/authors/create')]
    public function create(): View
    {
        $publishers = Publisher::select()->all();

        // Ensure database results are never null
        $publishers = $publishers ?? [];

        // Get validation errors and original values from session with null safety
        $sessionErrors = $this->session->get(Session::VALIDATION_ERRORS, []);
        $sessionOriginalValues = $this->session->get(Session::ORIGINAL_VALUES, []);

        // Ensure session data is always an array
        $errors = is_array($sessionErrors) ? $sessionErrors : [];
        $originalValues = is_array($sessionOriginalValues) ? $sessionOriginalValues : [];

        return view('Views/Authors/create.view.php',
            title: 'Create Author',
            publishers: $publishers,
            errors: $errors,
            originalValues: $originalValues
        );
    }

    #[Post('/authors')]
    public function store(CreateAuthorRequest $request): Redirect
    {
        // Validate required request parameters are not null
        if ($request->name === null || $request->type === null) {
            // This should be handled by validation, but adding defensive check
            return new Redirect('/authors/create');
        }

        $publisher = null;
        if (isset($request->publisherId) && $request->publisherId) {
            $publisher = Publisher::find(id: $request->publisherId)->first();

            // If publisherId was provided but publisher not found, redirect back
            if ($publisher === null) {
                return new Redirect('/authors/create');
            }
        }

        Author::create(name: $request->name, type: $request->type, publisher: $publisher);

        return new Redirect('/authors');
    }

    #[Get('/authors/{authorId}/edit')]
    public function edit(int $authorId): View|Redirect
    {
        $author = Author::find(id: $authorId)
            ->with('publisher')
            ->first();

        // Handle case where author is not found
        if ($author === null) {
            // Author not found - redirect to authors list instead of showing broken page
            return new Redirect('/authors');
        }

        $publishers = Publisher::select()->all();

        // Ensure database results are never null
        $publishers = $publishers ?? [];

        // Get validation errors and original values from session with null safety
        $sessionErrors = $this->session->get(Session::VALIDATION_ERRORS, []);
        $sessionOriginalValues = $this->session->get(Session::ORIGINAL_VALUES, []);

        // Ensure session data is always an array
        $errors = is_array($sessionErrors) ? $sessionErrors : [];
        $originalValues = is_array($sessionOriginalValues) ? $sessionOriginalValues : [];

        return view('Views/Authors/edit.view.php',
            title: 'Edit Author',
            author: $author,
            publishers: $publishers,
            errors: $errors,
            originalValues: $originalValues
        );
    }

    #[Post('/authors/{authorId}/update')]
    public function updatePost(int $authorId, UpdateAuthorRequest $request): Redirect
    {
        return $this->update($authorId, $request);
    }

    #[Put('/authors/{authorId}')]
    public function update(int $authorId, UpdateAuthorRequest $request): Redirect
    {
        // Validate required request parameters are not null
        if ($request->name === null || $request->type === null) {
            // This should be handled by validation, but adding defensive check
            return new Redirect('/authors/' . $authorId . '/edit');
        }

        $author = Author::find(id: $authorId)->first();

        // Ensure author exists
        if ($author === null) {
            // Author not found - redirect to authors list
            return new Redirect('/authors');
        }

        $publisher = null;
        if (isset($request->publisherId) && $request->publisherId) {
            $publisher = Publisher::find(id: $request->publisherId)->first();

            // If publisherId was provided but publisher not found, redirect back
            if ($publisher === null) {
                return new Redirect('/authors/' . $authorId . '/edit');
            }
        }

        $author->name = $request->name;
        $author->type = $request->type;
        $author->publisher = $publisher;
        $author->save();

        return new Redirect(to: '/authors/' . $authorId);
    }

    #[Delete('/authors/{authorId}')]
    public function delete(int $authorId): Redirect
    {
        $author = Author::find(id: $authorId)->first();

        // Explicit null check with better defensive programming
        if ($author !== null) {
            $author->delete();
        }
        // If author is null, we silently continue - author might already be deleted
        // or never existed, which is fine for a delete operation

        return new Redirect('/authors');
    }
}

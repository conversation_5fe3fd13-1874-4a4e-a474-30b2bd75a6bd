<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use Tempest\Http\Get;
use Tempest\Http\Response;
use Tempest\Http\Session\Session;

final readonly class DebugController
{
    public function __construct(
        private Session $session,
    ) {}

    #[Get('/debug/session')]
    public function debugSession(): Response
    {
        $validationErrors = $this->session->get(Session::VALIDATION_ERRORS, []);
        $originalValues = $this->session->get(Session::ORIGINAL_VALUES, []);
        
        $debug = [
            'validation_errors' => $validationErrors,
            'original_values' => $originalValues,
            'all_session_data' => $this->session->all(),
        ];
        
        return new Response(
            status: 200,
            body: '<pre>' . json_encode($debug, JSON_PRETTY_PRINT) . '</pre>',
            headers: ['Content-Type' => 'text/html']
        );
    }
}

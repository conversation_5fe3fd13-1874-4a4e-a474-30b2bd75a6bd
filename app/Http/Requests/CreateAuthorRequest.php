<?php

declare(strict_types=1);

namespace App\Http\Requests;

use App\Http\Requests\Exists;
use App\Models\AuthorType;
use App\Models\Publisher;
use Tempest\Http\IsRequest;
use Tempest\Http\Request;
use Tempest\Validation\Rules\Length;

final class CreateAuthorRequest implements Request
{
    use IsRequest;

    #[Length(min: 2, max: 100)]
    private(set) string $name;

    private(set) ?AuthorType $type = AuthorType::A;

    #[Exists(Publisher::class)]
    private(set) ?int $publisherId = null;
}

<x-base :title="$title">
    <main class="w-screen h-screen overflow-hidden bg-sky-100/20">
        <div class="relative isolate px-6 lg:px-8 flex flex-col items-center justify-center h-full">
            <!-- Background gradient -->
            <div class="pointer-events-none absolute inset-x-0 -top-40 -z-10 transform-gpu overflow-hidden blur-3xl sm:-top-80" aria-hidden="true">
                <div
                        class="relative left-[calc(50%-11rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 rotate-[30deg] bg-gradient-to-tr from-[#7fbdea] to-[#9980fc] opacity-20 sm:left-[calc(50%-30rem)] sm:w-[72.1875rem]"
                        style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"
                ></div>
            </div>
            <!-- Bottom gradient -->
            <div class="pointer-events-none absolute inset-x-0 top-[calc(100%-13rem)] -z-10 transform-gpu overflow-hidden blur-3xl sm:top-[calc(100%-30rem)]" aria-hidden="true">
                <div class="relative left-[calc(50%+3rem)] aspect-[1155/678] w-[36.125rem] -translate-x-1/2 bg-gradient-to-tr from-[#7fbdea] to-[#9980fc] opacity-20 sm:left-[calc(50%+36rem)] sm:w-[72.1875rem]" style="clip-path: polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)"></div>
            </div>
            <!-- Hero section -->
            <div class="mx-auto max-w-2xl py-32 sm:py-48 lg:py-56">
                <div class="text-center">
                    <a href="/authors">Back to Authors</a> | 
                    <a href="/authors/{{ $author->id }}/edit" class="text-blue-600 hover:text-blue-800">Edit</a> |
                    <form method="post" action="/authors/{{ $author->id }}" class="inline">
                        <x-csrf-token />
                        <input type="hidden" name="_method" value="DELETE" />
                        <button type="submit" class="text-red-600 hover:text-red-800" onclick="return confirm('Are you sure you want to delete this author?')">Delete</button>
                    </form>
                </div>
                <div class="text-center">
                    <h1 class="text-balance text-4xl font-semibold tracking-tight text-gray-500 sm:text-7xl">
                        {{ $author->name }}
                    </h1>
                    <p class="mt-8 text-pretty text-lg font-medium text-gray-500 sm:text-xl/8">
                        Type: {{ $author->type?->value ?? 'N/A' }}
                    </p>
                    <p class="mt-8 text-pretty text-lg font-medium text-gray-500 sm:text-xl/8">
                        Publisher:
                        <span :if="isset($author->publisher) && $author->publisher">
                            <a href="/publishers/{{ $author->publisher->id }}" class="text-purple-600 hover:text-purple-800">{{ $author->publisher->name }}</a>
                        </span>
                        <span :else>None</span>
                    </p>
                    <div class="mt-8">
                        <h2 class="text-2xl font-semibold text-gray-700">Books by this Author</h2>
                        <ul class="mt-4">
                            <li :foreach="$author->books ?? [] as $book">
                                <a href="/books/{{ $book->id }}" class="text-blue-600 hover:text-blue-800">{{ $book->title }}</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </main>
</x-base>

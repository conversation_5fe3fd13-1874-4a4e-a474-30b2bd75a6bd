<?php

declare(strict_types=1);

namespace App\Models;

use Tempest\Database\Id;
use Tempest\Database\IsDatabaseModel;
use Tempest\Database\Relations\HasMany;
use Tempest\Router\Bindable;

final class Publisher implements Bindable
{
    use IsDatabaseModel;

    public Id $id;

    public string $name;

    public string $description;

    /** @var \App\Models\Author[] */
    #[HasMany]
    public array $authors = [];

    public static function resolve(string $input): self
    {
        return self::find(id: $input)->first();
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id->id,
            'name' => $this->name,
            'description' => $this->description,
        ];
    }
}

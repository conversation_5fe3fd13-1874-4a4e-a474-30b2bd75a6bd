<?php

declare(strict_types=1);

namespace App\Models;

use Tempest\Database\IsDatabaseModel;
use Tempest\Database\Relations\BelongsTo;
use Tempest\Database\Relations\HasMany;
use Tempest\Router\Bindable;
use Tempest\Validation\Rules\Length;

final class Book implements Bindable
{
    use IsDatabaseModel;

    #[Length(min: 3, max: 50)]
    public string $title;

    #[BelongsTo]
    public Author $author;

    /** @var \App\Models\Chapter[] */
    #[HasMany]
    public array $chapters = [];

    public static function resolve(string $input): self
    {
        return self::find(id: $input)
            ->with('author')
            ->first();
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id->id,
            'title' => $this->title,
            'author' => $this->author->toArray(),
        ];
    }
}

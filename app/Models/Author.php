<?php

declare(strict_types=1);

namespace App\Models;

use Tempest\Database\BelongsTo;
use Tempest\Database\IsDatabaseModel;
use Tempest\Database\Relations\HasMany;
use Tempest\Router\Bindable;

class Author implements Bindable
{
    use IsDatabaseModel;

    public function __construct(
        public string $name,
        public ?AuthorType $type = AuthorType::A,

        /** @var \App\Models\Book[] */
        #[HasMany]
        public array $books = [],
        #[BelongsTo]
        public ?Publisher $publisher = null,
        public ?int $publisher_id = null,
    ) {}

    public static function resolve(string $input): self
    {
        return self::find(id: $input)
            ->with(relations: 'publisher')
            ->first();
    }



    public function toArray(): array
    {
        return [
            'id' => $this->id->id,
            'name' => $this->name,
            'type' => $this->type?->value,
            'publisher' => $this->publisher?->toArray(),
        ];
    }
}

<?php

namespace Rector\Tests\TypeDeclaration\Rector\RenameIdToPrimaryKeyRector\Fixture;

use Tempest\Database\Id;

final class Publisher
{
    use IsDatabaseModel;

    public Id $id;

    public string $name;

    public string $description;
}

?>
-----
<?php

namespace Rector\Tests\TypeDeclaration\Rector\RenameIdToPrimaryKeyRector\Fixture;

use Tempest\Database\PrimaryKey;

final class Publisher
{
    use IsDatabaseModel;

    public PrimaryKey $id;

    public string $name;

    public string $description;
}

?>

<?php

declare(strict_types=1);

namespace Utils\Rector\Rector;

use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Name as NodeName;
use <PERSON><PERSON><PERSON><PERSON><PERSON>\Node\Name\FullyQualified as FullyQualifiedNodeName;
use <PERSON>\Rector\AbstractRector;

/**
 * @see \Utils\Rector\Tests\TypeDeclaration\Rector\RenameIdToPrimaryKeyRector\RenameIdToPrimaryKeyRectorTest
 */
final class RenameIdToPrimaryKeyRector extends AbstractRector
{
    /**
     * @return array<class-string<Node>>
     */
    public function getNodeTypes(): array
    {
        return [\PhpParser\Node\Stmt\Property::class];
    }

    /**
     * @param \PhpParser\Node\Stmt\Property $node
     */
    public function refactor(Node $node): ?Node
    {
        if ($node->type === null) {
            return null;
        }

        if (!$node->type instanceof NodeName) {
            return null;
        }

        $className = $node->type->toString();
        if ($className !== 'Tempest\\Database\\Id') {
            return null;
        }

        if (! class_exists('Tempest\\Database\\PrimaryKey')) {
            return null;
        }

        $node->type = new FullyQualifiedNodeName('Tempest\\Database\\PrimaryKey');

        return $node;
    }
}

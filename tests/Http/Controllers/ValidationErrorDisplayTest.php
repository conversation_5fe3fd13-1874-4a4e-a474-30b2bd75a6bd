<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use App\Models\Author;
use App\Models\Publisher;
use PHPUnit\Framework\Attributes\Test;
use Tempest\Http\Status;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

final class ValidationErrorDisplayTest extends IntegrationTestCase
{
    #[Test]
    public function validation_errors_are_handled_properly_for_book_creation(): void
    {
        // Create a publisher and author for the test
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: 'a', publisher_id: $publisher_id)
            ->execute();

        // Test with invalid data (title too short)
        $response = $this->http->post('/books', [
            'title' => 'AB', // Too short (minimum 3 characters)
            'authorId' => $author_id,
        ]);

        // Should return BAD_REQUEST status
        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function validation_errors_are_handled_properly_for_author_creation(): void
    {
        // Test with invalid data (missing required name)
        $response = $this->http->post('/authors', [
            'name' => '', // Empty name should fail validation
            'type' => 'a',
        ]);

        // Should return BAD_REQUEST status
        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function validation_errors_are_handled_properly_for_publisher_creation(): void
    {
        // Test with invalid data (missing required fields)
        $response = $this->http->post('/publishers', [
            'name' => '', // Empty name should fail validation
            'description' => '',
        ]);

        // Should return BAD_REQUEST status
        $response->assertStatus(Status::BAD_REQUEST);
    }
}

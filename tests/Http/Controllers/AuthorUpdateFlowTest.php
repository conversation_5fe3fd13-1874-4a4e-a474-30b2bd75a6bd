<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use PHPUnit\Framework\Attributes\Test;
use Tempest\Http\Status;
use Tests\IntegrationTestCase;

final class AuthorUpdateFlowTest extends IntegrationTestCase
{
    #[Test]
    public function author_edit_form_loads_correctly(): void
    {
        // Test that the edit form loads without errors
        $response = $this->http->get('/authors/1/edit');
        $response->assertOk();
        
        // Check that the form contains expected elements
        $body = $response->getBody();
        $this->assertStringContainsString('Edit Author', $body);
        $this->assertStringContainsString('name=', $body);
        $this->assertStringContainsString('type', $body);
        $this->assertStringContainsString('publisherId', $body);
    }

    #[Test]
    public function author_update_with_invalid_data_returns_validation_error(): void
    {
        // Test that validation errors are properly handled
        $response = $this->http->put('/authors/1', [
            'name' => '', // Invalid - too short
            'type' => 'a',
        ]);

        // Should return validation error
        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function author_update_with_invalid_type_returns_validation_error(): void
    {
        // Test that invalid enum values are handled
        $response = $this->http->put('/authors/1', [
            'name' => 'Valid Name',
            'type' => 'invalid_type', // Invalid enum value
        ]);

        // Should return validation error
        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function author_update_with_nonexistent_publisher_returns_validation_error(): void
    {
        // Test that non-existent publisher IDs are handled
        $response = $this->http->put('/authors/1', [
            'name' => 'Valid Name',
            'type' => 'a',
            'publisherId' => 999999, // Non-existent publisher
        ]);

        // Should return validation error
        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function nonexistent_author_update_redirects_to_authors_list(): void
    {
        // Test that updating a non-existent author redirects correctly
        $response = $this->http->put('/authors/999', [
            'name' => 'Some Name',
            'type' => 'a',
        ]);

        // Should redirect to authors list
        $response->assertRedirect('/authors');
    }
}

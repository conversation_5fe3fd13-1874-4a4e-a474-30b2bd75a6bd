<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use App\Migrations\CreateAuthorTable;
use App\Migrations\CreateBookTable;
use App\Migrations\CreateChapterTable;
use App\Migrations\CreateIsbnTable;
use App\Migrations\CreatePublishersTable;
use App\Models\Author;
use App\Models\AuthorType;
use App\Models\Book;
use App\Models\Publisher;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tempest\Database\Migrations\CreateMigrationsTable;
use Tempest\Http\Status;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

final class AuthorControllerTest extends IntegrationTestCase
{
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->migrate(
            CreateMigrationsTable::class,
            CreatePublishersTable::class,
            CreateAuthorTable::class,
            CreateBookTable::class,
            CreateChapterTable::class,
            CreateIsbnTable::class,
        );
    }

    #[Test]
    public function get_author_listing(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $response = $this->http->get('/authors');

        $response->assertOk();
        $response->assertSee('Authors');
        $response->assertSee('Test Author');
        $response->assertSee('Test Publisher');
    }

    #[Test]
    public function get_empty_author_listing(): void
    {
        $response = $this->http->get('/authors');

        $response->assertOk();
        $response->assertSee('Authors');
        $response->assertSee('Library of authors');
    }

    #[Test]
    public function show_author_details(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        query(Book::class)
            ->insert(title: 'Test Book', author_id: $author_id)
            ->execute();

        $response = $this->http->get("/authors/{$author_id->id}");

        $response->assertOk();
        $response->assertSee('Test Author');
        $response->assertSee('Test Publisher');
        $response->assertSee('Test Book');
        $response->assertSee('Books by this Author');
    }

    #[Test]
    public function show_author_without_publisher(): void
    {
        $author_id = query(Author::class)
            ->insert(name: 'Independent Author', type: AuthorType::B, publisher_id: null)
            ->execute();

        $response = $this->http->get("/authors/{$author_id->id}");

        $response->assertOk();
        $response->assertSee('Independent Author');
        $response->assertSee('None');
    }

    #[Test]
    public function get_author_create_form(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $response = $this->http->get('/authors/create');

        $response->assertOk();
        $response->assertSee('New Author');
        $response->assertSee('Test Publisher');
    }

    #[Test]
    public function create_author_with_valid_data(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $response = $this->http->post('/authors', [
            'name' => 'New Author',
            'type' => 'a',
            'publisherId' => $publisher_id->id,
        ]);

        $response->assertRedirect(to: '/authors');

        $authors = Author::select()->all();
        self::assertCount(1, $authors);
        self::assertEquals('New Author', $authors[0]->name);
        self::assertEquals(AuthorType::A, $authors[0]->type);
    }

    #[Test]
    public function create_author_without_publisher(): void
    {
        $response = $this->http->post('/authors', [
            'name' => 'Independent Author',
            'type' => 'b',
            // Don't send publisherId at all for null case
        ]);

        $response->assertRedirect(to: '/authors');

        $authors = Author::select()->all();
        self::assertCount(1, $authors);
        self::assertEquals('Independent Author', $authors[0]->name);
        self::assertEquals(AuthorType::B, $authors[0]->type);
    }

    #[Test]
    public function create_author_with_invalid_data(): void
    {
        $response = $this->http->post('/authors', [
            'name' => 'A', // Too short
            'type' => 'invalid',
            'publisherId' => 999, // Non-existent publisher
        ]);

        $response->assertStatus(Status::BAD_REQUEST);
        self::assertCount(0, Author::select()->all());
    }

    #[Test]
    public function get_author_edit_form(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $response = $this->http->get("/authors/{$author_id->id}/edit");

        $response->assertOk();
        $response->assertSee('Edit Author');
        $response->assertSee('Test Author');
        $response->assertSee('Test Publisher');
    }

    #[Test]
    public function update_author_with_valid_data(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Original Publisher', description: 'Original Description')
            ->execute();

        $new_publisher_id = query(Publisher::class)
            ->insert(name: 'New Publisher', description: 'New Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Original Name', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $response = $this->http->put("/authors/{$author_id->id}", [
            'name' => 'Updated Name',
            'type' => 'b',
            'publisherId' => $new_publisher_id->id,
        ]);

        $response->assertRedirect(to: "/authors/{$author_id->id}");

        $updatedAuthor = Author::find(id: $author_id->id)->with('publisher')->first();
        self::assertEquals('Updated Name', $updatedAuthor->name);
        self::assertEquals(AuthorType::B, $updatedAuthor->type);
        self::assertEquals($new_publisher_id->id, $updatedAuthor->publisher->id->id);
    }

    #[Test]
    public function update_author_remove_publisher(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $response = $this->http->put("/authors/{$author_id->id}", [
            'name' => 'Updated Author',
            'type' => 'a',
            // Don't send publisherId to remove publisher
        ]);

        $response->assertRedirect(to: "/authors/{$author_id->id}");

        $updatedAuthor = Author::find(id: $author_id->id)->with('publisher')->first();
        self::assertEquals('Updated Author', $updatedAuthor->name);
        self::assertNull($updatedAuthor->publisher);
    }

    #[Test]
    public function update_author_with_invalid_data(): void
    {
        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: null)
            ->execute();

        $response = $this->http->put("/authors/{$author_id->id}", [
            'name' => 'X', // Too short
            'type' => 'invalid',
            'publisherId' => 999, // Non-existent publisher
        ]);

        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function delete_author_successfully(): void
    {
        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: null)
            ->execute();

        $response = $this->http->delete("/authors/{$author_id->id}");

        $response->assertRedirect(to: '/authors');

        self::assertCount(0, Author::select()->all());
    }

    #[Test]
    public function delete_non_existent_author(): void
    {
        $response = $this->http->delete('/authors/999');

        $response->assertRedirect(to: '/authors');
    }
}

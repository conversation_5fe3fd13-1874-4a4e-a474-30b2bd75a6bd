<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use App\Migrations\CreateAuthorTable;
use App\Migrations\CreateBookTable;
use App\Migrations\CreateChapterTable;
use App\Migrations\CreateIsbnTable;
use App\Migrations\CreatePublishersTable;
use App\Models\Author;
use App\Models\AuthorType;
use App\Models\Publisher;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tempest\Database\Migrations\CreateMigrationsTable;
use Tempest\Http\Status;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

final class PublisherControllerTest extends IntegrationTestCase
{
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->migrate(
            CreateMigrationsTable::class,
            CreatePublishersTable::class,
            CreateAuthorTable::class,
            CreateBookTable::class,
            CreateChapterTable::class,
            CreateIsbnTable::class,
        );
    }

    #[Test]
    public function get_publisher_listing(): void
    {
        query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $response = $this->http->get('/publishers');

        $response->assertOk();
        $response->assertSee('Publishers');
        $response->assertSee('Test Publisher');
        $response->assertSee('Test Description');
    }

    #[Test]
    public function get_empty_publisher_listing(): void
    {
        $response = $this->http->get('/publishers');

        $response->assertOk();
        $response->assertSee('Publishers');
        $response->assertSee('Library of publishers');
    }

    #[Test]
    public function show_publisher_details(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'A comprehensive test publisher description')
            ->execute();

        $response = $this->http->get("/publishers/{$publisher_id->id}");

        $response->assertOk();
        $response->assertSee('Test Publisher');
        $response->assertSee('A comprehensive test publisher description');
    }

    #[Test]
    public function get_publisher_create_form(): void
    {
        $response = $this->http->get('/publishers/create');

        $response->assertOk();
        $response->assertSee('New Publisher');
        $response->assertSee('Publisher Name');
        $response->assertSee('Publisher Description');
    }

    #[Test]
    public function create_publisher_with_valid_data(): void
    {
        $response = $this->http->post('/publishers', [
            'name' => 'New Publisher',
            'description' => 'This is a comprehensive description of the new publisher',
        ]);

        $response->assertRedirect(to: '/publishers');

        $publishers = Publisher::select()->all();
        self::assertCount(1, $publishers);
        self::assertEquals('New Publisher', $publishers[0]->name);
        self::assertEquals('This is a comprehensive description of the new publisher', $publishers[0]->description);
    }

    #[Test]
    public function create_publisher_with_invalid_name(): void
    {
        $response = $this->http->post('/publishers', [
            'name' => 'A', // Too short
            'description' => 'Valid description here',
        ]);

        $response->assertStatus(Status::BAD_REQUEST);
        self::assertCount(0, Publisher::select()->all());
    }

    #[Test]
    public function create_publisher_with_invalid_description(): void
    {
        $response = $this->http->post('/publishers', [
            'name' => 'Valid Publisher Name',
            'description' => 'Too', // Too short
        ]);

        $response->assertStatus(Status::BAD_REQUEST);
        self::assertCount(0, Publisher::select()->all());
    }

    #[Test]
    public function create_publisher_with_missing_data(): void
    {
        $response = $this->http->post('/publishers', [
            'name' => 'Valid Name',
            // Missing description
        ]);

        $response->assertStatus(Status::BAD_REQUEST);
        self::assertCount(0, Publisher::select()->all());
    }

    #[Test]
    public function get_publisher_edit_form(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $response = $this->http->get("/publishers/{$publisher_id->id}/edit");

        $response->assertOk();
        $response->assertSee('Edit Publisher');
        $response->assertSee('Test Publisher');
        $response->assertSee('Test Description');
    }

    #[Test]
    public function update_publisher_with_valid_data(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Original Name', description: 'Original description content')
            ->execute();

        $response = $this->http->put("/publishers/{$publisher_id->id}", [
            'name' => 'Updated Publisher Name',
            'description' => 'This is the updated description with more content',
        ]);

        $response->assertRedirect(to: "/publishers/{$publisher_id->id}");

        $updatedPublisher = Publisher::find(id: $publisher_id->id)->first();
        self::assertEquals('Updated Publisher Name', $updatedPublisher->name);
        self::assertEquals('This is the updated description with more content', $updatedPublisher->description);
    }

    #[Test]
    public function update_publisher_with_invalid_name(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $response = $this->http->put("/publishers/{$publisher_id->id}", [
            'name' => 'X', // Too short
            'description' => 'Valid description content',
        ]);

        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function update_publisher_with_invalid_description(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $response = $this->http->put("/publishers/{$publisher_id->id}", [
            'name' => 'Valid Publisher Name',
            'description' => 'Bad', // Too short
        ]);

        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function update_non_existent_publisher(): void
    {
        $response = $this->http->put('/publishers/999', [
            'name' => 'Valid Name',
            'description' => 'Valid description content',
        ]);

        $response->assertRedirect(to: '/publishers'); // Should redirect when publisher not found
    }

    #[Test]
    public function delete_publisher_successfully(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $response = $this->http->delete("/publishers/{$publisher_id->id}");

        $response->assertRedirect(to: '/publishers');

        self::assertCount(0, Publisher::select()->all());
    }

    #[Test]
    public function delete_non_existent_publisher(): void
    {
        $response = $this->http->delete('/publishers/999');

        $response->assertRedirect(to: '/publishers');
    }

    #[Test]
    public function delete_publisher_with_associated_authors(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        // This should fail due to foreign key constraint
        $this->expectException(\Tempest\Database\Exceptions\QueryWasInvalid::class);

        $this->http->delete("/publishers/{$publisher_id->id}");
    }
}

<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use PHPUnit\Framework\Attributes\Test;
use Tempest\Http\Status;
use Tests\IntegrationTestCase;

final class ValidationErrorWebTest extends IntegrationTestCase
{
    #[Test]
    public function publisher_form_shows_validation_errors_on_invalid_submission(): void
    {
        // First, get the create form
        $response = $this->http->get('/publishers/create');
        $response->assertOk();
        
        // Submit invalid data (empty name and description)
        $response = $this->http->post('/publishers', [
            'name' => '',
            'description' => '',
        ]);
        
        // Should return BAD_REQUEST status for validation failure
        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function author_form_shows_validation_errors_on_invalid_submission(): void
    {
        // First, get the create form
        $response = $this->http->get('/authors/create');
        $response->assertOk();
        
        // Submit invalid data (empty name)
        $response = $this->http->post('/authors', [
            'name' => '',
            'type' => 'a',
        ]);
        
        // Should return BAD_REQUEST status for validation failure
        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function forms_load_correctly_without_errors(): void
    {
        // Test that all forms load without errors
        $forms = [
            '/books/create',
            '/authors/create', 
            '/publishers/create',
        ];
        
        foreach ($forms as $form) {
            $response = $this->http->get($form);
            $response->assertOk();
            $this->assertStringContainsString('form', $response->getBody());
        }
    }
}

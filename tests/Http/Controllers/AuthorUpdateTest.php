<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use App\Models\Author;
use App\Models\Publisher;
use PHPUnit\Framework\Attributes\Test;
use Tempest\Http\Status;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

final class AuthorUpdateTest extends IntegrationTestCase
{
    #[Test]
    public function can_update_author_with_valid_data(): void
    {
        // Create a publisher first
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        // Create an author
        $author_id = query(Author::class)
            ->insert(name: 'Original Name', type: 'a', publisher_id: $publisher_id)
            ->execute();

        // Update the author
        $response = $this->http->put("/authors/{$author_id}", [
            'name' => 'Updated Name',
            'type' => 'b',
            'publisherId' => $publisher_id,
        ]);

        // Should redirect to the author show page
        $response->assertRedirect("/authors/{$author_id}");

        // Verify the author was updated in the database
        $updatedAuthor = Author::find(id: $author_id)->first();
        $this->assertNotNull($updatedAuthor);
        $this->assertEquals('Updated Name', $updatedAuthor->name);
        $this->assertEquals('b', $updatedAuthor->type->value);
    }

    #[Test]
    public function update_author_with_invalid_data_returns_validation_error(): void
    {
        // Create a publisher first
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        // Create an author
        $author_id = query(Author::class)
            ->insert(name: 'Original Name', type: 'a', publisher_id: $publisher_id)
            ->execute();

        // Try to update with invalid data (empty name)
        $response = $this->http->put("/authors/{$author_id}", [
            'name' => '', // Invalid - too short
            'type' => 'b',
            'publisherId' => $publisher_id,
        ]);

        // Should return validation error
        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function update_nonexistent_author_redirects_to_authors_list(): void
    {
        // Try to update a non-existent author
        $response = $this->http->put('/authors/999', [
            'name' => 'Some Name',
            'type' => 'a',
        ]);

        // Should redirect to authors list
        $response->assertRedirect('/authors');
    }
}

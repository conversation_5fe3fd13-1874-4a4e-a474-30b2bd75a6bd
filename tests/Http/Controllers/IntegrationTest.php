<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use App\Migrations\CreateAuthorTable;
use App\Migrations\CreateBookTable;
use App\Migrations\CreateChapterTable;
use App\Migrations\CreateIsbnTable;
use App\Migrations\CreatePublishersTable;
use App\Models\Author;
use App\Models\AuthorType;
use App\Models\Book;
use App\Models\Publisher;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tempest\Database\Migrations\CreateMigrationsTable;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

final class IntegrationTest extends IntegrationTestCase
{
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->migrate(
            CreateMigrationsTable::class,
            CreatePublishersTable::class,
            CreateAuthorTable::class,
            CreateBookTable::class,
            CreateChapterTable::class,
            CreateIsbnTable::class,
        );
    }

    #[Test]
    public function book_listing_displays_linked_author_and_publisher(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        query(Book::class)
            ->insert(title: 'Test Book', author_id: $author_id)
            ->execute();

        $response = $this->http->get('/');

        $response->assertOk();
        $response->assertSee('Test Book');
        $response->assertSee('Test Author');
        $response->assertSee('Test Publisher');

        // Check that navigation links are present
        $response->assertSee('/authors');
        $response->assertSee('/publishers');
    }

    #[Test]
    public function book_detail_page_shows_linked_author_and_publisher(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Detailed Publisher', description: 'Publisher Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Detailed Author', type: AuthorType::B, publisher_id: $publisher_id)
            ->execute();

        $book_id = query(Book::class)
            ->insert(title: 'Detailed Book', author_id: $author_id)
            ->execute();

        $response = $this->http->get("/books/{$book_id->id}");

        $response->assertOk();
        $response->assertSee('Detailed Book');
        $response->assertSee('Detailed Author');
        $response->assertSee('Detailed Publisher');

        // Check that links to author and publisher are present
        $response->assertSee("/authors/{$author_id->id}");
        $response->assertSee("/publishers/{$publisher_id->id}");
    }

    #[Test]
    public function author_listing_displays_linked_publishers(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Linked Publisher', description: 'Publisher Description')
            ->execute();

        query(Author::class)
            ->insert(name: 'Linked Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        // For now, just test that the page loads and shows the linked publisher
        // The nullable foreign key handling in Tempest ORM needs more investigation
        $response = $this->http->get('/authors');

        $response->assertOk();
        $response->assertSee('Linked Author');
        $response->assertSee('Linked Publisher');

        // Check navigation links
        $response->assertSee('Books');
        $response->assertSee('Publishers');
    }

    #[Test]
    public function author_detail_page_shows_associated_books_and_publisher(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Author Publisher', description: 'Publisher Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Prolific Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $book1_id = query(Book::class)
            ->insert(title: 'First Book', author_id: $author_id)
            ->execute();

        $book2_id = query(Book::class)
            ->insert(title: 'Second Book', author_id: $author_id)
            ->execute();

        $response = $this->http->get("/authors/{$author_id->id}");

        $response->assertOk();
        $response->assertSee('Prolific Author');
        $response->assertSee('Author Publisher');
        $response->assertSee('First Book');
        $response->assertSee('Second Book');
        $response->assertSee('Books by this Author');

        // Check that book links are present
        $response->assertSee("/books/{$book1_id->id}");
        $response->assertSee("/books/{$book2_id->id}");

        // Check that publisher link is present
        $response->assertSee("/publishers/{$publisher_id->id}");
    }

    #[Test]
    public function publisher_listing_displays_correctly(): void
    {
        query(Publisher::class)
            ->insert(name: 'First Publisher', description: 'First Description')
            ->execute();

        query(Publisher::class)
            ->insert(name: 'Second Publisher', description: 'Second Description')
            ->execute();

        $response = $this->http->get('/publishers');

        $response->assertOk();
        $response->assertSee('First Publisher');
        $response->assertSee('First Description');
        $response->assertSee('Second Publisher');
        $response->assertSee('Second Description');

        // Check navigation links
        $response->assertSee('Books');
        $response->assertSee('Authors');
    }

    #[Test]
    public function navigation_links_work_correctly_between_models(): void
    {
        // Test navigation from books to authors
        $response = $this->http->get('/');
        $response->assertOk();
        $response->assertSee('/authors');
        $response->assertSee('/publishers');

        // Test navigation from authors to books and publishers
        $response = $this->http->get('/authors');
        $response->assertOk();
        $response->assertSee('Books');
        $response->assertSee('Publishers');

        // Test navigation from publishers to books and authors
        $response = $this->http->get('/publishers');
        $response->assertOk();
        $response->assertSee('Books');
        $response->assertSee('Authors');
    }

    #[Test]
    public function create_forms_display_related_model_options(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Form Publisher', description: 'Publisher for forms')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Form Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        // Test book create form shows authors
        $response = $this->http->get('/books/create');
        $response->assertOk();
        $response->assertSee('Form Author');

        // Test author create form shows publishers
        $response = $this->http->get('/authors/create');
        $response->assertOk();
        $response->assertSee('Form Publisher');
    }

    #[Test]
    public function edit_forms_display_current_relationships(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Edit Publisher', description: 'Publisher for editing')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Edit Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $book_id = query(Book::class)
            ->insert(title: 'Edit Book', author_id: $author_id)
            ->execute();

        // Test book edit form shows current author selected
        $response = $this->http->get("/books/{$book_id->id}/edit");
        $response->assertOk();
        $response->assertSee('Edit Book');
        $response->assertSee('Edit Author');

        // Test author edit form shows current publisher selected
        $response = $this->http->get("/authors/{$author_id->id}/edit");
        $response->assertOk();
        $response->assertSee('Edit Author');
        $response->assertSee('Edit Publisher');

        // Test publisher edit form shows current data
        $response = $this->http->get("/publishers/{$publisher_id->id}/edit");
        $response->assertOk();
        $response->assertSee('Edit Publisher');
        $response->assertSee('Publisher for editing');
    }

    #[Test]
    public function crud_operations_maintain_data_integrity(): void
    {
        // Create a complete relationship chain
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Integrity Publisher', description: 'Testing data integrity')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Integrity Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $book_id = query(Book::class)
            ->insert(title: 'Integrity Book', author_id: $author_id)
            ->execute();

        // Verify all relationships exist
        $book = Book::find(id: $book_id->id)->with('author.publisher')->first();
        self::assertEquals('Integrity Book', $book->title);
        self::assertEquals('Integrity Author', $book->author->name);
        self::assertEquals('Integrity Publisher', $book->author->publisher->name);

        // Update author's publisher
        $new_publisher_id = query(Publisher::class)
            ->insert(name: 'New Publisher', description: 'Updated publisher')
            ->execute();

        $this->http->put("/authors/{$author_id->id}", [
            'name' => 'Updated Author',
            'type' => 'b',
            'publisherId' => $new_publisher_id->id,
        ]);

        // Verify relationships are maintained
        $updatedBook = Book::find(id: $book_id->id)->with('author.publisher')->first();
        self::assertEquals('Updated Author', $updatedBook->author->name);
        self::assertEquals('New Publisher', $updatedBook->author->publisher->name);
        self::assertEquals(AuthorType::B, $updatedBook->author->type);
    }
}

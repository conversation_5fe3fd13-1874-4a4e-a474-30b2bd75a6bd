<?php

declare(strict_types=1);

namespace Tests\Http\Controllers;

use App\Migrations\CreateAuthorTable;
use App\Migrations\CreateBookTable;
use App\Migrations\CreateChapterTable;
use App\Migrations\CreateIsbnTable;
use App\Migrations\CreatePublishersTable;
use App\Models\Author;
use App\Models\AuthorType;
use App\Models\Book;
use App\Models\Publisher;
use Override;
use PHPUnit\Framework\Attributes\Test;
use Tempest\Database\Migrations\CreateMigrationsTable;
use Tempest\Http\Status;
use Tests\IntegrationTestCase;

use function Tempest\Database\query;

final class HomeControllerTest extends IntegrationTestCase
{
    #[Override]
    protected function setUp(): void
    {
        parent::setUp();

        $this->migrate(
            CreateMigrationsTable::class,
            CreatePublishersTable::class,
            CreateAuthorTable::class,
            CreateBookTable::class,
            CreateChapterTable::class,
            CreateIsbnTable::class,
        );
    }

    #[Test]
    public function get_book_listing(): void
    {
        $this->http->get('/')->assertOk();
    }

    #[Test]
    public function show_book_details(): void
    {
        $this->http->get('/books/1')->assertOk();
    }

    #[Test]
    public function create_new_book_details(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Greece Publishers', description: 'The Greece Publishers Guild')
            ->execute();

        $authorId = query(Author::class)
            ->insert(name: 'Homer', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $response = $this->http
            ->post('/books', [
                'title' => 'Homer',
                'authorId' => $authorId->id,
            ]);

        $response->assertRedirect(to: '/');

        self::assertCount(1, Book::select()->all());
    }

    #[Test]
    public function get_book_edit_form(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $book_id = query(Book::class)
            ->insert(title: 'Test Book', author_id: $author_id)
            ->execute();

        $response = $this->http->get("/books/{$book_id->id}/edit");

        $response->assertOk();
        $response->assertSee('Edit Book');
        $response->assertSee('Test Book');
    }

    #[Test]
    public function update_book_with_valid_data(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $new_author_id = query(Author::class)
            ->insert(name: 'New Author', type: AuthorType::B, publisher_id: $publisher_id)
            ->execute();

        $book_id = query(Book::class)
            ->insert(title: 'Original Title', author_id: $author_id)
            ->execute();

        $response = $this->http->put("/books/{$book_id->id}", [
            'title' => 'Updated Title',
            'authorId' => $new_author_id->id,
        ]);

        $response->assertRedirect(to: "/books/{$book_id->id}");

        $updatedBook = Book::find(id: $book_id->id)->with('author')->first();
        self::assertEquals('Updated Title', $updatedBook->title);
        self::assertEquals($new_author_id->id, $updatedBook->author->id->id);
    }

    #[Test]
    public function update_book_with_invalid_data(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $book_id = query(Book::class)
            ->insert(title: 'Test Book', author_id: $author_id)
            ->execute();

        $response = $this->http->put("/books/{$book_id->id}", [
            'title' => 'AB', // Too short
            'authorId' => 999, // Non-existent author
        ]);

        $response->assertStatus(Status::BAD_REQUEST);
    }

    #[Test]
    public function delete_book_successfully(): void
    {
        $publisher_id = query(Publisher::class)
            ->insert(name: 'Test Publisher', description: 'Test Description')
            ->execute();

        $author_id = query(Author::class)
            ->insert(name: 'Test Author', type: AuthorType::A, publisher_id: $publisher_id)
            ->execute();

        $book_id = query(Book::class)
            ->insert(title: 'Test Book', author_id: $author_id)
            ->execute();

        $response = $this->http->delete("/books/{$book_id->id}");

        $response->assertRedirect(to: '/');

        self::assertCount(0, Book::select()->all());
    }

    #[Test]
    public function delete_non_existent_book(): void
    {
        $response = $this->http->delete('/books/999');

        $response->assertRedirect(to: '/');
    }
}
